/**
 * Script de debug para identificar problemas de navegação
 * Monitora cliques em links e identifica conflitos
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Navigation Debug iniciado');

    // Monitora todos os cliques em links
    document.addEventListener('click', function(event) {
        const element = event.target;
        const link = element.closest('a');
        
        if (link) {
            console.group('🔗 Clique em link detectado');
            console.log('Link:', link);
            console.log('URL:', link.href);
            console.log('Tem wire:navigate:', link.hasAttribute('wire:navigate'));
            console.log('Tem data-flux:', link.hasAttribute('data-flux-navlist-item') || link.hasAttribute('data-flux-navbar-item') || link.hasAttribute('data-flux-menu-item'));
            console.log('Event target:', element);
            console.log('Event currentTarget:', event.currentTarget);
            console.log('Event defaultPrevented:', event.defaultPrevented);
            console.log('Event propagationStopped:', event.cancelBubble);
            console.groupEnd();
        }
    }, true); // Usa capture para pegar antes de outros handlers

    // Monitora eventos do Livewire
    document.addEventListener('livewire:init', function() {
        console.log('🚀 Livewire inicializado');
        
        // Hook para navegação
        Livewire.hook('navigate', ({ url, options }) => {
            console.log('🧭 Livewire navigate:', url, options);
        });

        // Hook para requests
        Livewire.hook('request', ({ uri, options, payload, respond, succeed, fail }) => {
            console.log('📡 Livewire request:', uri);
            
            succeed(({ status, response }) => {
                console.log('✅ Livewire request success:', status);
            });
            
            fail(({ status, content, preventDefault }) => {
                console.log('❌ Livewire request fail:', status, content);
            });
        });
    });

    // Monitora eventos de Alpine.js
    document.addEventListener('alpine:init', function() {
        console.log('⛰️ Alpine.js inicializado');
    });

    // Monitora mudanças de URL
    let currentUrl = window.location.href;
    const urlObserver = new MutationObserver(function() {
        if (window.location.href !== currentUrl) {
            console.log('🔄 URL mudou de:', currentUrl, 'para:', window.location.href);
            currentUrl = window.location.href;
        }
    });

    urlObserver.observe(document, { subtree: true, childList: true });

    // Monitora eventos de popstate
    window.addEventListener('popstate', function(event) {
        console.log('⬅️ Popstate event:', event.state);
    });

    // Monitora eventos de beforeunload
    window.addEventListener('beforeunload', function(event) {
        console.log('👋 Página sendo descarregada');
    });

    // Função para testar navegação
    window.testNavigation = function(url) {
        console.log('🧪 Testando navegação para:', url);
        if (window.Livewire) {
            Livewire.navigate(url);
        } else {
            console.error('Livewire não disponível');
        }
    };

    // Função para verificar estado do Livewire
    window.checkLivewireState = function() {
        console.group('📊 Estado do Livewire');
        console.log('Livewire disponível:', !!window.Livewire);
        console.log('Alpine disponível:', !!window.Alpine);
        console.log('Componentes Livewire:', document.querySelectorAll('[wire\\:id]').length);
        console.log('Elementos Alpine:', document.querySelectorAll('[x-data]').length);
        console.groupEnd();
    };

    // Função para verificar links problemáticos
    window.checkProblematicLinks = function() {
        console.group('🔍 Verificando links problemáticos');
        
        const allLinks = document.querySelectorAll('a[href]');
        console.log('Total de links:', allLinks.length);
        
        const wireNavigateLinks = document.querySelectorAll('a[wire\\:navigate]');
        console.log('Links com wire:navigate:', wireNavigateLinks.length);
        
        const fluxLinks = document.querySelectorAll('[data-flux-navlist-item], [data-flux-navbar-item], [data-flux-menu-item]');
        console.log('Links Flux UI:', fluxLinks.length);
        
        // Verifica links sem href válido
        const invalidLinks = Array.from(allLinks).filter(link => !link.href || link.href === '#');
        console.log('Links inválidos:', invalidLinks.length, invalidLinks);
        
        // Verifica links com múltiplos event listeners
        allLinks.forEach((link, index) => {
            const events = getEventListeners ? getEventListeners(link) : {};
            if (Object.keys(events).length > 0) {
                console.log(`Link ${index} tem eventos:`, events);
            }
        });
        
        console.groupEnd();
    };

    console.log('🎯 Debug functions disponíveis:');
    console.log('- testNavigation(url)');
    console.log('- checkLivewireState()');
    console.log('- checkProblematicLinks()');
});
