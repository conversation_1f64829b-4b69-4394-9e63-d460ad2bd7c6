@props([
    'user',
    'src' => null,
    'alt' => '',
    'size' => 'md',
    'class' => ''
])

@php
    // Determine avatar URL
    $avatarUrl = $src;
    if (!$avatarUrl && $user) {
        $currentPhoto = $user->currentPhoto ?? $user->photos->first();
        $avatarUrl = $currentPhoto ? \App\Helpers\StorageHelper::getAvatarUrl($currentPhoto->photo_path) : asset('images/default-avatar.jpg');
    }
    $avatarUrl = $avatarUrl ?: asset('images/default-avatar.jpg');
    
    // Determine alt text
    $altText = $alt ?: ($user ? $user->name : 'Avatar');
    
    // Get ranking info
    $rankingBorderClass = $user ? \App\Helpers\RankingHelper::getRankingBorderClass($user) : '';
    
    // Size classes
    $sizeClasses = match ($size) {
        'xs' => 'w-6 h-6',
        'sm' => 'w-8 h-8',
        'lg' => 'w-16 h-16',
        'xl' => 'w-24 h-24',
        '2xl' => 'w-32 h-32',
        default => 'w-10 h-10'
    };
@endphp

<div class="ranking-avatar {{ $rankingBorderClass }}">
    <img
        src="{{ $avatarUrl }}"
        alt="{{ $altText }}"
        class="{{ $sizeClasses }} rounded-full object-cover {{ $class }}"
        {{ $attributes }}
    >
</div>
