@props([
    'label' => null,
    'accept' => null,
    'multiple' => false,
    'error' => null,
    'id' => 'file-' . uniqid(),
    'showFilename' => true,
    'icon' => 'document-text',
    'iconVariant' => 'outline',
    'required' => false,
    'help' => null,
])

<div {{ $attributes->only(['class'])->merge(['class' => 'w-full']) }}>
    @if($label)
        <label for="{{ $id }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif

    <div class="relative">
        <label for="{{ $id }}" class="flex items-center justify-center w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
            <x-flux::icon :name="$icon" :variant="$iconVariant" class="w-5 h-5 mr-2" />
            <span>Escolher arquivo{{ $multiple ? 's' : '' }}</span>
            <input
                {{ $attributes->except(['class']) }}
                type="file"
                id="{{ $id }}"
                class="sr-only"
                @if($accept) accept="{{ $accept }}" @endif
                @if($multiple) multiple @endif
                @if($required) required @endif
            >
        </label>
    </div>

    @if($help)
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ $help }}</p>
    @endif

    @if($error)
        <p class="mt-1 text-sm text-red-600 dark:text-red-500">{{ $error }}</p>
    @endif


    <div wire:loading {{ $attributes->wire('model') }} class="mt-2">
        <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div class="bg-red-600 h-2 rounded-full animate-pulse" style="width: 100%"></div>
        </div>
        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">Carregando arquivo...</div>
    </div>

    
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // File input validation
        const fileInput = document.getElementById('{{ $id }}');

        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                const files = e.target.files;

                if (!files || files.length === 0) {
                    return;
                }

                // Validate files
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];

                    if (!file) {
                        console.error('Invalid file detected');
                        e.target.value = '';
                        return;
                    }

                    // Check file size (200MB limit)
                    const maxSize = 200 * 1024 * 1024;
                    if (file.size > maxSize) {
                        alert('Arquivo muito grande. Tamanho máximo: 200MB');
                        e.target.value = '';
                        return;
                    }
                }
            });
        }
    });
</script>
