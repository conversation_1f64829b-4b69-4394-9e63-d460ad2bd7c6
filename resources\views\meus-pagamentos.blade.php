<x-layouts.app :title="__('Meus Pagamentos')">
    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        Meus Pagamentos
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300 mt-1">
                        Histórico de pagamentos enviados e recebidos
                    </p>
                </div>
                <div class="flex gap-3">
                    <flux:button href="{{ route('wallet.index') }}" variant="outline">
                        <flux:icon name="wallet" class="h-4 w-4 mr-2" />
                        <PERSON>a Carteira
                    </flux:button>
                </div>
            </div>
        </div>

        <!-- Estatísticas -->
        @php
            $totalSent = $payments->where('sender_id', auth()->id())->sum('amount');
            $totalReceived = $payments->where('user_id', auth()->id())->sum('amount');
            $completedPayments = $payments->where('status', 'completed')->count();
            $pendingPayments = $payments->where('status', 'pending')->count();
        @endphp

        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <flux:icon name="arrow-down-left" class="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Recebido</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">R$ {{ number_format($totalReceived, 2, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                        <flux:icon name="arrow-up-right" class="h-6 w-6 text-red-600 dark:text-red-400" />
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Enviado</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">R$ {{ number_format($totalSent, 2, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <flux:icon name="check-circle" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Concluídos</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $completedPayments }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <flux:icon name="clock" class="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pendentes</p>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $pendingPayments }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Pagamentos -->
        <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg overflow-hidden">
            @if($payments->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-zinc-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Tipo
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Usuário
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Valor
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Data
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Método
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($payments as $payment)
                                @php
                                    $isReceived = $payment->user_id === auth()->id();
                                    $otherUser = $isReceived ? $payment->sender : $payment->user;
                                    $userPhoto = $otherUser?->currentPhoto ?? $otherUser?->userPhotos->first();
                                    $avatarUrl = $userPhoto ? \Illuminate\Support\Facades\Storage::url($userPhoto->photo_path) : asset('images/default-avatar.jpg');
                                @endphp
                                <tr class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            @if($isReceived)
                                                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-full">
                                                    <flux:icon name="arrow-down-left" class="h-4 w-4 text-green-600 dark:text-green-400" />
                                                </div>
                                                <span class="ml-2 text-sm font-medium text-green-600 dark:text-green-400">Recebido</span>
                                            @else
                                                <div class="p-2 bg-red-100 dark:bg-red-900 rounded-full">
                                                    <flux:icon name="arrow-up-right" class="h-4 w-4 text-red-600 dark:text-red-400" />
                                                </div>
                                                <span class="ml-2 text-sm font-medium text-red-600 dark:text-red-400">Enviado</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="{{ $avatarUrl }}" alt="{{ $otherUser->name }}" class="w-10 h-10 rounded-full object-cover">
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $otherUser->name }}</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $otherUser->email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                        R$ {{ number_format($payment->amount, 2, ',', '.') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($payment->status === 'completed')
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                Concluído
                                            </span>
                                        @elseif($payment->status === 'pending')
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                                Pendente
                                            </span>
                                        @else
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                                Cancelado
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ $payment->created_at->format('d/m/Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ $payment->payment_method ?? 'Stripe' }}
                                    </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="p-6 text-center">
                    <flux:icon name="credit-card" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhum pagamento encontrado</h3>
                    <p class="text-gray-500 dark:text-gray-400">Você ainda não fez ou recebeu nenhum pagamento.</p>
                </div>
            @endif
        </div>
    </div>
</x-layouts.app>
</x-layouts.app>